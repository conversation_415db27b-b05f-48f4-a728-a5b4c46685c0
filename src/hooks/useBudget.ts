import { useState, useCallback, useEffect } from 'react';
import { agent<PERSON><PERSON> } from '@/services/agentApi';
import { useToast } from './use-toast';

interface BudgetInfo {
  max_budget: number;
  current_usage: number;
}

interface UseBudgetOptions {
  jobId?: string;
  initialFetch?: boolean;
}

interface UseBudgetReturn {
  budgetInfo: BudgetInfo | null;
  isLoading: boolean;
  error: string | null;
  fetchBudgetInfo: () => Promise<BudgetInfo | undefined>;
  updateBudget: (amount: number, showFeedback?: boolean) => Promise<BudgetInfo | undefined>;
}

/**
 * Custom hook for managing budget information for a job
 * 
 * @param options Configuration options for the hook
 * @returns Budget information and functions to fetch and update budget
 */
export function useBudget({ jobId, initialFetch = true }: UseBudgetOptions = {}): UseBudgetReturn {
  const [budgetInfo, setBudgetInfo] = useState<BudgetInfo | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(initialFetch);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  /**
   * Fetches the current budget information for the job
   */
  const fetchBudgetInfo = useCallback(async (): Promise<BudgetInfo | undefined> => {
    if (!jobId) {
      setError('No job ID provided');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const budget = await agentApi.getBudget(jobId);      
      const updatedBudgetInfo = {
        max_budget: budget.max_budget,
        current_usage: budget.current_usage
      };
      
      setBudgetInfo(updatedBudgetInfo);
      return updatedBudgetInfo;
    } catch (error) {
      console.error("Error fetching budget info:", error);
      setError(error instanceof Error ? error.message : 'Failed to fetch budget information');
      return undefined;
    } finally {
      setIsLoading(false);
    }
  }, [jobId]);

  /**
   * Updates the budget for the job
   * 
   * @param amount The new budget amount
   */
  const updateBudget = useCallback(async (amount: number, showFeedback: boolean = true): Promise<BudgetInfo | undefined> => {
    if (!jobId) {
      setError('No job ID provided');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      const budget = await agentApi.updateBudget(jobId, amount);
      
      const updatedBudgetInfo = {
        max_budget: budget.max_budget,
        current_usage: budget.current_usage
      };
      
      setBudgetInfo(updatedBudgetInfo);
      
      if (showFeedback) {
        toast({
          title: "Budget Updated",
          description: `Successfully increased budget to ${updatedBudgetInfo.max_budget.toFixed(3)} Credits`,
        });
      }
      
      return updatedBudgetInfo;
    } catch (error) {
      console.error("Error updating budget:", error);
      setError(error instanceof Error ? error.message : 'Failed to update budget');
      
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update budget",
        variant: "destructive",
      });
      
      return undefined;
    } finally {
      setIsLoading(false);
    }
  }, [jobId, toast]);

  // Fetch budget info on initial render if initialFetch is true
  useEffect(() => {
    if (initialFetch && jobId) {
      fetchBudgetInfo();
    }
  }, [initialFetch, jobId, fetchBudgetInfo]);

  return {
    budgetInfo,
    isLoading,
    error,
    fetchBudgetInfo,
    updateBudget
  };
}
