import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useLazyCheckProModePaymentQuery } from '@/store/api/apiSlice';
import { useCredits } from '@/contexts';

export interface UseProModePaymentReturn {
  isProModeUser: boolean;
  isLoading: boolean;
  error: string | null;
  paymentData: any | null;
  checkPaymentStatus: () => void;
}

export const useProModePayment = (): UseProModePaymentReturn => {
  const { user } = useAuth();
  const {tier} = useCredits();
  const [isProModeUser, setIsProModeUser] = useState(false);
  const [paymentData, setPaymentData] = useState(null);
  const [error, setError] = useState<string | null>(null);

  const [checkProModePayment, { isLoading }] = useLazyCheckProModePaymentQuery();

  // Function to check payment status
  const checkPaymentStatus = useCallback(async () => {
    if (!user?.email) {
      setError('User email not available');
      return;
    }

    try {
      setError(null);
      const result = await checkProModePayment({
        tag: 'pro_mode',
      }).unwrap();

      if (result.exists && result.payment?.payment_status === 'paid') {
        setIsProModeUser(true);
        setPaymentData(result?.payment as any);
      } else {
        setIsProModeUser(false);
        setPaymentData(null);
        localStorage.removeItem('pro_mode_status');
      }
    } catch (err: any) {
      console.error('Failed to check pro mode payment:', err);
      setError(err.message || 'Failed to verify payment status');
      setIsProModeUser(false);
      setPaymentData(null);
    }
  }, [user?.email, checkProModePayment]);

  // Check localStorage on mount for existing pro mode status
  useEffect(() => {
    if (user?.email && tier != "free") {
            checkPaymentStatus();
    }
  }, [user?.email, tier]);

  return {
    isProModeUser,
    isLoading,
    error,
    paymentData,
    checkPaymentStatus,
  };
};
