import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { TabContent, cleanupUnusedStores } from './TabContent';
import {
  setActiveTab as setActiveTabAction,
  setTabs as setTabsAction,
  updateTabState as updateTabStateAction,
  removeTab as removeTabAction,
  setPendingMessage as setPendingMessageAction,
  clearPendingMessage as clearPendingMessageAction,
  removePendingMessage as removePendingMessageAction,
  cleanupTabStates as cleanupTabStatesAction,
} from '@/store/tabSlice';
import { trackTabInteraction } from '@/services/postHogService';
import { useDeploy } from '@/hooks/useDeploy';

// hooks
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks';
import { selectActiveTab, selectActiveTabData, selectTabs, } from '@/store/tabSlice';
import { createSelector } from '@reduxjs/toolkit';


// Types
import { ResponseImageData } from '@/types/message';

// Memoized selector for pending messages
const selectAllPendingMessages = createSelector(
  [(state: any) => state.tabs.tabStates],
  (tabStates) => {
    const messages: PendingMessagesMap = {};
    Object.entries(tabStates).forEach(([tabId, tabState]: [string, any]) => {
      if (tabState.pendingMessage) {
        messages[tabId] = tabState.pendingMessage;
      }
    });
    return messages;
  }
);

// Assets

import Tab from './Tab';
import MobileTabSidebar from './MobileTabSidebar';
import { useIsEmergentUser } from '@/hooks/useIsEmergentUser';
import MenuIcons from "@/assets/menu-icons/menu.svg"

export interface ITab {
  id: string;
  title: string;
  path: string;
  created_by?: string;
  state?: {
    containerId?: string;
    setupInProgress?: boolean;
    copyToApp?: boolean;
    sourceTabId?: string;
    fromTabBar?: boolean;
    projectName?: string;
    projectPath?: string;
    logo_url?: string;
    selectedImage?: string;
    task?: string;
    jobId?: string;
    trajPath?: string;
    [key: string]: any;
  };
}

interface TabState {
  activeTab: string;
  tabs: ITab[];
  isActive: boolean;
  pendingMessage?: {
    id: string;
    content: string;
    timestamp: string;
    base64_image_list: ResponseImageData[];
  };
}

interface PendingMessagesMap {
  [tabId: string]: {
    id: string;
    content: string;
    timestamp: string;
    base64_image_list: ResponseImageData[];
  } | undefined;
}

interface TabContextType {
  state: TabState;
  activeTab: string;
  isActive: boolean;
  setActiveTab: (id: string) => void;
  setTabs: React.Dispatch<React.SetStateAction<ITab[]>>;
  updateTabState: (tabId: string, newState: any) => void;
  removeTab: (tabId: string) => void;
  getTabState: (tabId: string) => any;
  getActiveTab: () => ITab;
  setPendingMessage: (tabId: string, messageId: string, content: string, base64_image_list: ResponseImageData[]) => void;
  clearPendingMessage: (tabId: string, content?: string) => boolean;
  getPendingMessage: (tabId: string) => { id: string; content: string; timestamp: string } | undefined;
  getTabByJobId: (jobId: string) => ITab | undefined;
  removePendingMessage: (tabId: string, content: string) => void;
  getAllTabs: () => ITab[];
  cleanupTabStates: () => void;
}

const TabContext = React.createContext<TabContextType | null>(null);

export function useTabState() {
  const context = React.useContext(TabContext);
  if (!context) {
    throw new Error('useTabState must be used within a TabProvider');
  }
  return context;
}

export function TabProvider({ children }: { children: React.ReactNode }) {

  const dispatch = useAppDispatch();

  // Redux State

  const allTabStates = useAppSelector(state => state.tabs.tabStates);
  const allPendingMessages = useAppSelector(selectAllPendingMessages);
  const activeTabData = useAppSelector(selectActiveTabData);
  const tabsWithJobIds = useAppSelector(state => state.tabs.tabs);
  const activeTab = useAppSelector(selectActiveTab);
  const tabs = useAppSelector(selectTabs);
  const isActive = useAppSelector(state => state.tabs.isActive)

  // Clean up tab states that don't have corresponding tabs
  const cleanupTabStates = useCallback(() => {
    dispatch(cleanupTabStatesAction());
  }, [dispatch]);

  // Local State

  const state = useMemo(() => ({
    activeTab,
    tabs,
    isActive
  }), [
    activeTab,
    tabs,
    isActive
  ])

  const setActiveTab = useCallback((id: string) => {
    dispatch(setActiveTabAction(id))
  }, [dispatch]);


  const setTabs = useCallback((action: React.SetStateAction<ITab[]>) => {
    const newTabs = typeof action === "function" ? action(tabs) : action;
    dispatch(setTabsAction(newTabs));
  }, [dispatch, tabs])

  const getAllTabs = useCallback(() => {
    return tabs;
  }, [tabs]);


  const updateTabState = useCallback((tabId: string, newState: any) => {
    dispatch(updateTabStateAction({ tabId, state: newState }));
  }, [dispatch]);


  const getTabState = useCallback((tabId: string) => allTabStates[tabId] || {}, [allTabStates])


  const getTabByJobId = useCallback(
    (jobId: string) => tabsWithJobIds.find(tab => tab.state?.jobId === jobId),
    [tabsWithJobIds]
  );

  const removeTab = useCallback((tabId: string) => {
    dispatch(removeTabAction(tabId));
  }, [dispatch]);




  const setPendingMessage = useCallback((tabId: string, messageId: string, content: string, base64_image_list: ResponseImageData[]) => {
    dispatch(setPendingMessageAction({ tabId, messageId, content, base64_image_list }));
  }, [dispatch]);


  const getActiveTab = useCallback(
    () => activeTabData,
    [activeTabData]
  );


  const clearPendingMessage = useCallback((tabId: string, content?: string) => {

    if (content) {
      const pendingMessage = allPendingMessages[tabId];
      if (!pendingMessage || pendingMessage.content !== content) {
        return false;
      }
    }

    dispatch(clearPendingMessageAction({ tabId, content }));
    return true;
  }, [dispatch, allPendingMessages]);


  const removePendingMessage = useCallback((tabId: string, content: string) => {
    dispatch(removePendingMessageAction({ tabId, content }));
  }, [dispatch]);

  const getPendingMessage = useCallback(
    (tabId: string) => allPendingMessages[tabId],
    [allPendingMessages]
  );


  const value = useMemo(() => ({
    state,
    activeTab,
    setActiveTab,
    setTabs,
    updateTabState,
    getTabState,
    setPendingMessage,
    clearPendingMessage,
    getPendingMessage,
    getActiveTab,
    removeTab,
    getTabByJobId,
    removePendingMessage,
    getAllTabs,
    isActive,
    cleanupTabStates
  }), [
    state,
    activeTab,
    isActive,
    setActiveTab,
    setTabs,
    updateTabState,
    getTabState,
    setPendingMessage,
    clearPendingMessage,
    getPendingMessage,
    removeTab,
    getActiveTab,
    getTabByJobId,
    removePendingMessage,
    getAllTabs,
    cleanupTabStates
  ]);

  useEffect(() => {
    if (activeTab && tabs.length > 0) {
      const activeTabExists = tabs.some(tab => tab.id === activeTab);
      if (!activeTabExists) {
        // Add more detailed logging to understand the race condition
        console.warn(`Active tab "${activeTab}" not found in tabs array. Available tabs:`, tabs.map(t => t.id));
        console.warn('This might be a race condition. Waiting 100ms before switching to home...');

        // Add a small delay to allow for React state updates to complete
        setTimeout(() => {
          const currentTabs = tabs;
          const stillNotExists = !currentTabs.some(tab => tab.id === activeTab);
          if (stillNotExists) {
            console.warn(`Tab "${activeTab}" still not found after delay. Setting to home.`);
            setActiveTab('home');
          } else {
            console.log(`Tab "${activeTab}" found after delay. No need to switch to home.`);
          }
        }, 100);
      } else {
        // Make sure the Redux state is in sync with the tab component's state
        // This ensures that the tab is properly selected in the UI
        tabs.forEach(tab => {
          const isTabActive = tab.id === activeTab;
          const tabState = getTabState(tab.id);

          // Only update if the isActive property is incorrect
          if (tabState.isActive !== isTabActive) {
            updateTabState(tab.id, { isActive: isTabActive });
          }
        });
      }
    }

    // Clean up tab states that don't have corresponding tabs
    cleanupTabStates();
  }, [activeTab, tabs, setActiveTab, getTabState, updateTabState, cleanupTabStates]);

  return (
    <TabContext.Provider value={value}>
      {children}
    </TabContext.Provider>
  );
}

export function TabContents() {
  const { state: { activeTab, tabs } } = useTabState();
  const [previousActiveTab, setPreviousActiveTab] = React.useState<string>(activeTab);
  const [unmountTimer, setUnmountTimer] = React.useState<NodeJS.Timeout | null>(null);
  const [visibleTabs, setVisibleTabs] = React.useState<Set<string>>(new Set([activeTab, 'home']));

  // Update visible tabs when active tab changes
  React.useEffect(() => {
    // Clear any existing timer
    if (unmountTimer) {
      clearTimeout(unmountTimer);
    }

    // Always keep the active tab visible
    setVisibleTabs(prev => {
      const newSet = new Set(prev);
      newSet.add(activeTab);
      return newSet;
    });

    // Set a timer to unmount the previous tab after a delay
    // This prevents flickering during quick tab switches
    if (previousActiveTab !== activeTab) {
      const timer = setTimeout(() => {
        setVisibleTabs(prev => {
          const newSet = new Set(prev);
          // Always keep the home tab mounted for quick access
          if (previousActiveTab !== 'home') {
            newSet.delete(previousActiveTab);
          }

          // Clean up Redux stores for unmounted tabs
          cleanupUnusedStores(newSet);

          return newSet;
        });
      }, 15000); // 5 second delay before unmounting

      setUnmountTimer(timer);
      setPreviousActiveTab(activeTab);
    }

    return () => {
      if (unmountTimer) {
        clearTimeout(unmountTimer);
      }
    };
  }, [activeTab, previousActiveTab, unmountTimer]);

  return (
    <>
      {tabs.map((tab) => {
        const isVisible = visibleTabs.has(tab.id);

        // Only render the tab if it's visible
        return (
          <div key={tab.id}
            className={cn('h-full', {
              hidden: tab.id !== activeTab
            })}>
            {isVisible && <TabContent key={tab.id} path={tab.path} tabId={tab.id} />}
          </div>
        );
      })}
    </>
  );
}

export default function TabBar() {
  const { state: { activeTab, tabs }, setActiveTab, setTabs } = useTabState();
  const navigate = useNavigate();
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [tabWidth, setTabWidth] = useState(180); // Reduced default tab width
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);


  const { setActiveTabId, checkDeployStatus, handleTabClosed } = useDeploy();

  const lastCheckedTabRef = useRef<{tabId: string, jobId: string | undefined}>({
    tabId: '',
    jobId: undefined
  });

  useEffect(() => {
    const calculateTabWidth = () => {
      if (!tabsContainerRef.current) return;

      const nonHomeTabCount = tabs.filter(tab => tab.id !== 'home' && tab.path !== "/not-defined").length;
      const containerWidth = tabsContainerRef.current.clientWidth;
      const homeTabWidth = 80;
      const plusButtonWidth = 40;
      const scrollButtonsWidth = showScrollButtons ? 70 : 0;
      const availableWidth = containerWidth - homeTabWidth - plusButtonWidth - scrollButtonsWidth;
      const defaultTabWidth = 180;
      const minTabWidth = 100;

      if (nonHomeTabCount > 0) {
        if ((defaultTabWidth * nonHomeTabCount) <= availableWidth) {
          setTabWidth(defaultTabWidth);
          setShowScrollButtons(false);
        } else {
          const calculatedWidth = Math.floor(availableWidth / nonHomeTabCount);
          const newWidth = Math.max(minTabWidth, calculatedWidth);
          setTabWidth(newWidth);
          setShowScrollButtons(true);
        }
      }
    };

    calculateTabWidth();

    const resizeObserver = new ResizeObserver(() => {
      calculateTabWidth();
    });

    if (tabsContainerRef.current) {
      resizeObserver.observe(tabsContainerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [tabs.length, showScrollButtons]);

  // Handle scrolling tabs
  const scrollTabs = useCallback((direction: 'left' | 'right') => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const scrollAmount = direction === 'left' ? -200 : 200;
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  }, []);

  // Handle navigation after tab changes
  useEffect(() => {
    const activeTabData = tabs.find(tab => tab.id === activeTab);
    if (activeTabData) {
      navigate(activeTabData.path);
    }
  }, [activeTab, navigate, tabs]);

  const handleTabClick = useCallback((tab: ITab) => {
    if (tab.id !== activeTab) {

      // Track tab switched event
      trackTabInteraction('tab_switched', {
        tabId: tab.id,
        jobId: tab.state?.jobId,
        containerId: tab.state?.containerId,
        path: tab.path,
        title: tab.title,
        reason: 'user_clicked'
      });

      setActiveTab(tab.id);
      setActiveTabId(tab.id);

      // Reset the last checked reference when manually switching tabs
      lastCheckedTabRef.current = {
        tabId: '',
        jobId: undefined
      };

      if (tab.state?.jobId && !tab.state?.showCase) {
        setTimeout(() => {
          checkDeployStatus(tab.state!.jobId!, false);
        }, 100);
      }
    }
  }, [setActiveTab, activeTab, setActiveTabId, checkDeployStatus]);

  const handleTabClose = useCallback((e: React.MouseEvent, tab: ITab) => {
    e.stopPropagation();
    const newTabs = tabs.filter((t) => t.id !== tab.id);
    setTabs(newTabs);

    // Track tab closed event
    trackTabInteraction('tab_closed', {
      tabId: tab.id,
      jobId: tab.state?.jobId,
      containerId: tab.state?.containerId,
      path: tab.path,
      title: tab.title,
      isActive: tab.id === activeTab,
      remainingTabCount: newTabs.length
    });

    if (tab.state?.jobId) {
      handleTabClosed(tab.id);
    } else {
      handleTabClosed(tab.id);
    }

    if (tab.id === activeTab && newTabs.length > 0) {
      const validTabs = newTabs.filter((t) => t.path !== "/not-defined");

      if (validTabs.length > 0) {
        const lastTab = validTabs[validTabs.length - 1];
        setActiveTab(lastTab.id);
        navigate(lastTab.path);

        lastCheckedTabRef.current = {
          tabId: '',
          jobId: undefined
        };

        trackTabInteraction('tab_switched', {
          tabId: lastTab.id,
          jobId: lastTab.state?.jobId,
          containerId: lastTab.state?.containerId,
          path: lastTab.path,
          title: lastTab.title,
          reason: 'active_tab_closed'
        });
      } else if (newTabs.length > 0) {
        const lastTab = newTabs[newTabs.length - 1];
        setActiveTab(lastTab.id);
        navigate(lastTab.path);

        // Reset the last checked reference when switching tabs after closing
        lastCheckedTabRef.current = {
          tabId: '',
          jobId: undefined
        };

        // Track tab switched event (after closing active tab)
        trackTabInteraction('tab_switched', {
          tabId: lastTab.id,
          jobId: lastTab.state?.jobId,
          containerId: lastTab.state?.containerId,
          path: lastTab.path,
          title: lastTab.title,
          reason: 'active_tab_closed'
        });
      }
    } else if (newTabs.length > 0) {
      const validTab = newTabs.find((t) => t.path !== "/not-defined");

      if (validTab) {
        setActiveTab(validTab.id);
        navigate(validTab.path);

        // Reset the last checked reference when switching tabs after closing
        lastCheckedTabRef.current = {
          tabId: '',
          jobId: undefined
        };
      } else {
        setActiveTab(newTabs[0].id);
        navigate(newTabs[0].path);

        // Reset the last checked reference when switching tabs after closing
        lastCheckedTabRef.current = {
          tabId: '',
          jobId: undefined
        };
      }
    }
  }, [activeTab, setActiveTab, setTabs, navigate, tabs, handleTabClosed]);


  const handleNewTab = useCallback(() => {
    setActiveTab('home');

    // Reset the last checked reference when going to home tab
    lastCheckedTabRef.current = {
      tabId: '',
      jobId: undefined
    };
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && !e.altKey && !e.shiftKey) {
        const key = parseInt(e.key);

        const visibleTabs = tabs.filter(tab => tab.path !== "/not-defined" &&
          localStorage.getItem('embeddedTaskTabId') !== tab.id);

        if (!isNaN(key) && key >= 0 && key <= 9) {
          e.preventDefault();

          if (key === 0) {
            const homeTab = tabs.find(tab => tab.id === 'home');
            if (homeTab) {
              setActiveTab(homeTab.id);
            }
          } else if (key <= visibleTabs.length) {
            const targetTab = visibleTabs[key - 1];
            if (targetTab) {
              setActiveTab(targetTab.id);
            }
          }
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [tabs, setActiveTab]);


  const tabList = useMemo(() => {
    return tabs.map(tab => (
      <Tab
        key={tab.id}
        tab={tab}
        isActive={tab.id === activeTab}
        tabWidth={tabWidth}
        onTabClick={handleTabClick}
        onTabClose={handleTabClose}
      />
    ));
  }, [tabs, activeTab, tabWidth, handleTabClick, handleTabClose]);

  return (
    <>
      {/* Mobile Tab Sidebar */}
      <MobileTabSidebar
        isOpen={isMobileSidebarOpen}
        onClose={() => setIsMobileSidebarOpen(false)}
        tabs={tabs}
        activeTab={activeTab}
        onTabClick={handleTabClick}
        onTabClose={handleTabClose}
        onNewTab={handleNewTab}
      />

      <div className="relative flex items-center w-full pt-3">
        {/* Mobile Sidebar Toggle Button - Only visible on mobile */}
        <button
          type="button"
          title="Show Tabs"
          onClick={() => setIsMobileSidebarOpen(true)}
          className="z-10 flex-shrink-0 ml-2 mr-2 rounded-md hover:bg-secondary/50 md:hidden"
        >
          <img src={MenuIcons} alt="Menu" className="w-6 h-6" />
        </button>

        {showScrollButtons && (
          <button
            type='button'
            title='Scroll Left'
            onClick={() => scrollTabs('left')}
            className="p-1.5 hover:bg-secondary/50 rounded-md flex-shrink-0 z-10 hidden md:block"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#7B7B80]">
              <path d="M15 19l-7-7 7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        )}

        {/* Tab list - Hidden on mobile, visible on desktop */}
        <div
          ref={tabsContainerRef}
          className="relative items-center flex-grow hidden space-x-0 overflow-x-auto md:flex md:space-x-1 scrollbar-hide"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {tabList}

          {/* Plus Icon -> Redirects to Home Page */}
          {
            activeTab !== 'home' && <div className="flex items-center space-x-2 transition-all ease-in-out opacity-50 cursor-pointer hover:opacity-100" onClick={handleNewTab}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" clipRule="evenodd" d="M12 5C12.4296 5.00001 12.7778 5.34822 12.7778 5.77778L12.7778 11.2222L18.2222 11.2222C18.6518 11.2222 19 11.5704 19 12C19 12.4295 18.6518 12.7778 18.2222 12.7778L12.7778 12.7777L12.7778 18.2222C12.7778 18.6518 12.4296 18.9999 12 19C11.5704 18.9999 11.2222 18.6518 11.2222 18.2222L11.2222 12.7777L5.77778 12.7778C5.34824 12.7778 5.00002 12.4295 5 12C5.00004 11.5704 5.34823 11.2222 5.77782 11.2222L11.2222 11.2222L11.2222 5.77777C11.2222 5.34822 11.5704 5.00001 12 5Z" fill="#ffffff" />
              </svg>
            </div>
          }
        </div>

        {/* Mobile Active Tab Display */}
        <div className="flex-1 min-w-0 px-2 md:hidden">
          <div className="pb-1 text-[15px] font-medium capitalize truncate max-w-[80%] text-white/80">
            {tabs.find(tab => tab.id === activeTab)?.title != 'Home' ? tabs.find(tab => tab.id === activeTab)?.title : ''}
          </div>
        </div>

        {showScrollButtons && (
          <button
            type="button"
            title='Scroll Right'
            onClick={() => scrollTabs('right')}
            className="p-1.5 hover:bg-secondary/50 rounded-md flex-shrink-0 z-10 hidden md:block"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#7B7B80]">
              <path d="M9 19l7-7-7-7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        )}
      </div>
    </>
  );
}