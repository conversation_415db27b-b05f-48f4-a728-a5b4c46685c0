import React, { useEffect, useState } from "react";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collpasible";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import CopyButton from "../CopyButton";
import QRCode from "qrcode";

interface QRCodeModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  previewUrl: string;
}

export const QRCodeModal: React.FC<QRCodeModalProps> = ({
  isOpen,
  onOpenChange,
  previewUrl,
}) => {
  const { isMobile } = useScreenSize();
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [expoQrCodeDataUrl, setExpoQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingExpo, setIsGeneratingExpo] = useState(false);
  const [expoSectionOpen, setExpoSectionOpen] = useState(false);
  const [previewSectionOpen, setPreviewSectionOpen] = useState(true);

  // Convert https:// URL to exp:// URL for Expo
  const uuid = previewUrl.replace(/^https:\/\//, "").split(".")[0];
  const expUrl = `exp://${uuid}.ngrok.io`;

  // Expo Go download URL
  const expoGoUrl = "https://expo.dev/client";

  useEffect(() => {
    if (isOpen) {
      // Generate QR code for app preview
      if (expUrl) {
        setIsGenerating(true);
        QRCode.toDataURL(expUrl, {
          width: 256,
          margin: 2,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
        })
          .then((dataUrl: any) => {
            setQrCodeDataUrl(dataUrl);
          })
          .catch((error: any) => {
            console.error("Error generating app preview QR code:", error);
          })
          .finally(() => {
            setIsGenerating(false);
          });
      }

      // Generate QR code for Expo Go download
      setIsGeneratingExpo(true);
      QRCode.toDataURL(expoGoUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      })
        .then((dataUrl: any) => {
          setExpoQrCodeDataUrl(dataUrl);
        })
        .catch((error: any) => {
          console.error("Error generating Expo Go QR code:", error);
        })
        .finally(() => {
          setIsGeneratingExpo(false);
        });
    }
  }, [isOpen, expUrl, expoGoUrl]);

  // Shared content component
  const renderContent = ({
    showHeader = false,
  }: { showHeader?: boolean } = {}) => (
    <div className="relative space-y-6">
      {showHeader && (
        <div className="flex flex-col space-y-2">
          <DialogTitle className="text-xl font-medium text-white">
            Preview on your phone
          </DialogTitle>
          <DialogDescription className="text-sm text-[#9CA3AF]">
            Download Expo Go app to see your app in your phone or just directly
            scan the preview QR to see it in your native browser
          </DialogDescription>
        </div>
      )}

      {/* Accordion Sections */}
      <div className="space-y-5">
        {/* Download Expo Go Section */}
        <Collapsible
          open={expoSectionOpen}
          onOpenChange={setExpoSectionOpen}
          className="bg-[#202021] rounded-lg overflow-hidden"
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-5 border-none text-left hover:bg-[#252526] transition-colors">
            <h3 className="text-lg font-medium text-white flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M2 15C2 14.4477 2.44772 14 3 14C3.55228 14 4 14.4477 4 15C4 17.2091 5.79087 19 8 19H16C18.2091 19 20 17.2091 20 15C20 14.4477 20.4477 14 21 14C21.5523 14 22 14.4477 22 15C22 18.3137 19.3137 21 16 21H8C4.68629 21 2 18.3137 2 15ZM11 4C11 3.44772 11.4477 3 12 3C12.5523 3 13 3.44772 13 4V12.9844C13.4293 12.5461 13.8312 12.0809 14.2002 11.5889C14.5315 11.1471 15.1578 11.0574 15.5996 11.3887C16.0414 11.72 16.131 12.3463 15.7998 12.7881C15.0103 13.8408 14.0944 14.7917 13.0732 15.6191V15.6201C12.761 15.873 12.3801 16 12 16C11.6199 16 11.239 15.873 10.9268 15.6201V15.6191C10.0333 14.8951 9.22012 14.0772 8.50195 13.1787L8.2002 12.7881L8.14258 12.7031C7.88133 12.269 7.98631 11.6992 8.40039 11.3887C8.81456 11.0782 9.39089 11.1372 9.73438 11.5098L9.7998 11.5889L10.0645 11.9297C10.3583 12.2973 10.6712 12.6487 11 12.9844V4Z"
                  fill="white"
                  fill-opacity="0.8"
                />
              </svg>
              Download Expo Go App
            </h3>
            <svg
              className={`w-5 h-5 text-[#9CA3AF] transition-transform duration-200 ${expoSectionOpen ? "" : "-rotate-90"}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </CollapsibleTrigger>

          <CollapsibleContent className="data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up overflow-hidden">
            <div className="space-y-4 p-5 pt-0">
              <p className="text-sm text-[#9CA3AF]">
                Scan this QR code to install the Expo Go app from the Play Store.
              </p>

              <div className="flex items-center rounded-lg p-4 px-0">
                <div className="flex items-center w-full justify-between space-x-2">
                  {/* QR Code for Expo Go download */}
                  <div className="bg-[#FFFFFF08] rounded-lg justify-center items-center flex flex-1 p-4">
                    {isGeneratingExpo ? (
                      <div className="w-16 h-16 flex items-center justify-center bg-[#FFFFFF08] rounded">
                        <div className="text-gray-500 text-xs">Loading...</div>
                      </div>
                    ) : expoQrCodeDataUrl ? (
                      <img
                        src={expoQrCodeDataUrl}
                        alt="QR Code for Expo Go download"
                        className="w-48 h-48 rounded-lg opacity-80"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-black flex items-center justify-center text-white text-xs">
                        QR Code
                      </div>
                    )}
                  </div>

                  <div className="flex justify-center items-center flex-1 h-full rounded-2xl border border-1 border-white/5 p-4">
                    {/* <div className="w-12 h-12 bg-[#2A2A2B] rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-lg">Expo</span>
                    </div> */}
                    <div className="bg-white/10 rounded-[10px] flex space-x-2 items-center justify-center py-[10px] px-4">
                      <p className="text-white/80 font-medium text-sm">
                        Download Expo Go
                      </p>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M5.16634 4.615C5.37713 3.9978 5.77576 3.46201 6.30634 3.08274C6.83693 2.70346 7.47289 2.49969 8.12509 2.5H14.3751C15.2039 2.5 15.9987 2.82924 16.5848 3.41529C17.1709 4.00134 17.5001 4.7962 17.5001 5.625V11.875C17.5001 13.2475 16.6151 14.4125 15.3851 14.8337V15.2887C15.3851 15.7075 15.2388 16.2425 14.8951 16.6838C14.5338 17.1488 13.9601 17.5 13.1738 17.5H5.96134C5.50582 17.5032 5.05421 17.4157 4.63276 17.2428C4.21131 17.0699 3.82844 16.815 3.50638 16.4928C3.18433 16.1707 2.92952 15.7877 2.75677 15.3662C2.58402 14.9447 2.49677 14.493 2.50009 14.0375V6.82875C2.50009 6.1425 2.74634 5.57125 3.18259 5.175C3.60759 4.78875 4.16384 4.61625 4.71134 4.61625L5.16634 4.615ZM5.00009 5.865H4.71259C4.46122 5.85373 4.21483 5.93765 4.02259 6.1C3.87509 6.23375 3.75009 6.455 3.75009 6.8275V14.0387C3.75009 15.28 4.72009 16.2513 5.96259 16.2513H13.1738C13.5476 16.2513 13.7663 16.1 13.9088 15.9163C14.0455 15.7346 14.124 15.5158 14.1338 15.2887V15H8.12509C7.29629 15 6.50143 14.6708 5.91538 14.0847C5.32933 13.4987 5.00009 12.7038 5.00009 11.875V5.865ZM10.0001 7.5H11.6163L8.30759 10.8075C8.24948 10.8656 8.20339 10.9346 8.17194 11.0105C8.14049 11.0864 8.1243 11.1678 8.1243 11.25C8.1243 11.3322 8.14049 11.4136 8.17194 11.4895C8.20339 11.5654 8.24948 11.6344 8.30759 11.6925C8.3657 11.7506 8.43469 11.7967 8.51061 11.8282C8.58654 11.8596 8.66791 11.8758 8.75009 11.8758C8.83227 11.8758 8.91365 11.8596 8.98957 11.8282C9.0655 11.7967 9.13448 11.7506 9.19259 11.6925L12.5001 8.38375V10C12.5001 10.1658 12.5659 10.3247 12.6831 10.4419C12.8004 10.5592 12.9593 10.625 13.1251 10.625C13.2909 10.625 13.4498 10.5592 13.567 10.4419C13.6842 10.3247 13.7501 10.1658 13.7501 10V6.875C13.7501 6.70924 13.6842 6.55027 13.567 6.43306C13.4498 6.31585 13.2909 6.25 13.1251 6.25H10.0001C9.83433 6.25 9.67536 6.31585 9.55815 6.43306C9.44094 6.55027 9.37509 6.70924 9.37509 6.875C9.37509 7.04076 9.44094 7.19973 9.55815 7.31694C9.67536 7.43415 9.83433 7.5 10.0001 7.5Z"
                          fill="white"
                          fill-opacity="0.8"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Preview QR Code Section */}
        <Collapsible
          open={previewSectionOpen}
          onOpenChange={setPreviewSectionOpen}
          className="bg-[#202021] rounded-lg overflow-hidden"
        >
          <CollapsibleTrigger className="flex items-center justify-between w-full p-5 border-none text-left hover:bg-[#252526] transition-colors">
            <h3 className="text-lg font-medium text-white flex items-center gap-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M9 16C9 15.4477 8.5523 15 8 15H6C5.44771 15 5 15.4477 5 16V18C5 18.5523 5.44771 19 6 19H8C8.5523 19 9 18.5523 9 18V16ZM13 20V19.9902C13 19.438 13.4477 18.9902 14 18.9902C14.5523 18.9902 15 19.438 15 19.9902V20C15 20.5523 14.5523 21 14 21C13.4477 21 13 20.5523 13 20ZM17 20V18C17 17.4477 17.4477 17 18 17H20C20.5523 17 21 17.4477 21 18C21 18.5523 20.5523 19 20 19H19V20C19 20.5523 18.5523 21 18 21C17.4477 21 17 20.5523 17 20ZM13 14C13 13.4477 13.4477 13 14 13C14.5523 13 15 13.4477 15 14V15H16C16.5523 15 17 15.4477 17 16C17 16.5523 16.5523 17 16 17H14C13.4477 17 13 16.5523 13 16V14ZM20 13C20.5523 13 21 13.4477 21 14C21 14.5523 20.5523 15 20 15H18C17.4477 15 17 14.5523 17 14C17 13.4477 17.4477 13 18 13H20ZM9 6C9 5.44771 8.55229 5 8 5H6C5.44771 5 5 5.44771 5 6V8C5 8.55229 5.44771 9 6 9H8C8.55229 9 9 8.55229 9 8V6ZM19 6C19 5.44771 18.5523 5 18 5H16C15.4477 5 15 5.44771 15 6V8C15 8.5523 15.4477 9 16 9H18C18.5523 9 19 8.5523 19 8V6ZM11 18C11 19.6569 9.65685 21 8 21H6C4.34315 21 3 19.6569 3 18V16C3 14.3431 4.34315 13 6 13H8C9.65685 13 11 14.3431 11 16V18ZM11 8C11 9.65685 9.65685 11 8 11H6C4.34315 11 3 9.65685 3 8V6C3 4.34315 4.34315 3 6 3H8C9.65685 3 11 4.34315 11 6V8ZM21 8C21 9.65685 19.6569 11 18 11H16C14.3431 11 13 9.65685 13 8V6C13 4.34315 14.3431 3 16 3H18C19.6569 3 21 4.34315 21 6V8Z"
                  fill="white"
                />
              </svg>
              Preview QR Code
            </h3>
            <svg
              className={`w-5 h-5 text-[#9CA3AF] transition-transform duration-200 ${previewSectionOpen ? "rotate-90" : "rotate-none"}`}
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </CollapsibleTrigger>

          <CollapsibleContent className="data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up overflow-hidden">
            <div className="p-5 pt-0 flex flex-col items-start space-y-4">
              {/* Main QR Code Display */}
              <div className="text-start space-y-2 w-full">
                <p className="text-sm text-[#9CA3AF]">
                  Scan this QR code with your camera to preview the app
                </p>
              </div>
              <div className="bg-[#FFFFFF08] p-4 rounded-lg">
                {isGenerating ? (
                  <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                    <div className="text-gray-500 text-sm">
                      Generating QR code...
                    </div>
                  </div>
                ) : qrCodeDataUrl ? (
                  <img
                    src={qrCodeDataUrl}
                    alt="QR Code for app preview"
                    className="w-48 h-48 rounded-lg opacity-80"
                  />
                ) : (
                  <div className="w-48 h-48 flex items-center justify-center bg-gray-100 rounded">
                    <div className="text-gray-500 text-sm">
                      Failed to generate QR code
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );

  // Shared footer component
  const renderFooter = () => (
    <div className="flex-row justify-end w-full flex gap-4">
      <Button
        className="px-6 py-2 bg-transparent border border-[#404040] text-white hover:bg-[#2A2A2B]"
        variant="outline"
        onClick={() => onOpenChange(false)}
      >
        Close
      </Button>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title=""
        description=""
        open={isOpen}
        onOpenChange={onOpenChange}
        maxWidth="max-w-full"
        showDefaultFooter={false}
        footer={renderFooter()}
      >
        <div className="h-[70dvh] flex flex-col px-4 pt-4 bg-[#18181A]">
          {renderContent({ showHeader: true })}
        </div>
      </BottomSheet>
    );
  }

  // For desktop, use Dialog
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="m-4 mx-auto max-w-[calc(100vw-32px)] sm:max-w-xl w-xl bg-[#18181A] border-[#242424] text-white">
        <DialogHeader className="pb-4 bg-[#18181A] space-y-2">
          <DialogTitle className="text-[22px] font-medium text-white">
            Preview on your phone
          </DialogTitle>
          <DialogDescription className="text-sm text-[#737780]">
            Download Expo Go app to see your app in your phone or just directly
            scan the preview QR to see it in your native browser
          </DialogDescription>
        </DialogHeader>
        <div className="px-6 pb-6 pt-5">{renderContent()}</div>
      </DialogContent>
    </Dialog>
  );
};
