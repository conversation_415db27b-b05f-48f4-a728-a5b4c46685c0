import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import AgentSleepingSVG from "@/assets/agentsleeping.svg";
import AgentBell from "@/assets/fluent-emoji_bell.svg";
import { useCodeDownload } from "@/hooks/useCodeDownload";

interface GitHubDetails {
  branch: string;
  repo: string;
  owner: string;
  provider: string;
}

interface AgentSleepNotificationProps {
  podIsPaused: boolean;
  isInputActive: boolean;
  showCase: boolean;
  githubDetails: GitHubDetails | null;
  createdBefore25April: boolean;
  archivedPod: boolean;
  resumePod: () => void;
  isWakingUp: boolean;
  jobId?: string;
}

export const AgentSleepNotification: React.FC<AgentSleepNotificationProps> = ({
  podIsPaused,
  isInputActive,
  showCase,
  githubDetails,
  createdBefore25April,
  resumePod,
  isWakingUp,
  archivedPod,
  jobId,
}) => {
  const { isDownloading, handleDownload } = useCodeDownload();

  // Don't show if not paused, not active, or in showcase mode
  if (!podIsPaused || !isInputActive || showCase) {
    return null;
  }

  // Support email link
  const supportLink = (
    <a
      href="help.emergent.sh"
      className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-semibold underline underline-offset-1"
    >
      Help Center
    </a>
  );

  // Get status message based on conditions
  const getStatusMessage = () => {
    const baseClass = "text-[#7b7b80] font-['Inter'] text-sm";

    if (githubDetails && createdBefore25April) {
      return (
        <span className={baseClass}>
          Apologies, the task has expired due to inactivity.
          We may not be able to recover this at this moment.
          Please start a new task using the same github repo
          or visit our {supportLink} for any assistance.
        </span>
      );
    }

    if (githubDetails) {
      return (
        <span className={baseClass}>
          Your GitHub task is paused. Click 'Wake up the Agent' to continue.
          Need help? Visit our {supportLink}
        </span>
      );
    }

    if (archivedPod) {
      return (
        <span className={baseClass}>
          This job has been archived. You can download your code and create a new job.
          Need help? Visit our {supportLink}
        </span>
      );
    }

    return (
      <span className={baseClass}>
        If you are having trouble accessing your work,
        please visit our {supportLink}
      </span>
    );
  };

  const mainMessage = archivedPod ? "Job has been archived." : "Agent went to sleep due to inactivity.";
  const showWakeUpButton = !(githubDetails && createdBefore25April) && !archivedPod;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ type: "tween", ease: "easeOut", duration: 0.4 }}
        className="absolute px-4 grayscale-0 bottom-0 left-0 right-0 z-[49] w-full"
      >
        <div className="max-w-4xl mx-auto bg-[#1D1D1E] rounded-[1rem] rounded-bl-none rounded-br-none overflow-hidden p-2 pb-0 flex justify-between">
          <div className="w-full bg-[#131314] p-4 md:px-[2rem] rounded-[1rem] rounded-bl-none rounded-br-none md:pt-[2rem] md:pb-[3rem] flex md:flex-row flex-col justify-between md:items-center gap-3">

            {/* Content section */}
            <div className="flex items-center gap-5">
              <img
                src={AgentSleepingSVG}
                className="hidden w-8 h-8 md:block md:min-w-12 md:min-h-12"
                alt="Agent Sleeping"
              />
              <div className="flex flex-col gap-1 font-inter">
                <span className="text-sm md:text-[16px] font-medium text-white">
                  {mainMessage}
                </span>
                {getStatusMessage()}
              </div>
            </div>

            {/* Action buttons */}
            {showWakeUpButton && (
              <button
                type="button"
                onClick={resumePod}
                disabled={isWakingUp}
                className="bg-[#FCB94920] max-w-[200px] disabled:opacity-25 p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
              >
                <img src={AgentBell} alt="Wake icon" className="w-5 h-5" />
                <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent text-[12px] md:text-[16px] bg-clip-text font-medium">
                  Wake up the Agent
                </span>
              </button>
            )}

            {archivedPod && (
              <button
                type="button"
                onClick={() => handleDownload(jobId)}
                disabled={isDownloading || !jobId}
                className="bg-[#FCB94920] max-w-[200px] justify-center disabled:opacity-25 p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
              >
                <span className="bg-gradient-to-r text-nowrap from-[#FCB949] to-[#E28C37] text-transparent text-[12px] md:text-[16px] bg-clip-text font-medium">
                  {isDownloading ? "Downloading..." : "Download Your Code"}
                </span>
              </button>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
